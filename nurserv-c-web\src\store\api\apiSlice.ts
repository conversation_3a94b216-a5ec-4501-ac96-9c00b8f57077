import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import type { CustomerRegistrationData } from '@/types/auth';

const baseUrl =
  import.meta.env.VITE_API_BASE_URL ||
  'https://testcustomerapi.nurserv.com/api';

interface CreateBookingData {
  nurse_cognitoId: string;
  customer_cognitoId: string;
  nurse_given_name: string;
  nurse_family_name: string;
  customer_given_name: string;
  nurse_location_latitude: string;
  nurse_location_longitude: string;
  nurse_location_address: string;
  customer_booked_location_address: string;
  customer_booked_location_latitude: string;
  customer_booked_location_longitude: string;
  services_selected: string[];
  hourly_fare: string;
  booked_date: string;
  booked_slot: string;
}
interface createBookingResponse {
  message: string;
  booking_id: number;
  booking_status: string;
  booking_date: string;
  id: number;
  nurse: Nurse;
}

export interface Booking {
  id: number;
  booking_id: number;
  nurse_cognitoId: string;
  customer_cognitoId: string;
  nurse_given_name: string;
  nurse_family_name: string;
  customer_given_name: string;
  nurse_location_latitude: string;
  nurse_location_longitude: string;
  nurse_location_address: string;
  customer_booked_location_address: string;
  customer_booked_location_latitude: string;
  customer_booked_location_longitude: string;
  hourly_fare: string;
  booked_date: string;
  booked_slot: string;
  booking_status: string;
  created_at: string;
  updated_at: string;
  cancellation_reason?: string;
  nurse_profile_image_signed_url?: string;
  nurse_profile_s3_key?: string;
  profile_image_name?: string;
  services_selected?: string;
}

export interface GetBookingsByCustomerResponse {
  total_bookings: number;
  bookings: Booking[];
}

export interface UpdateBookingStatusRequest {
  booking_id: number;
  booking_status:
    | 'Pending'
    | 'Accepted'
    | 'Declined'
    | 'Cancelled'
    | 'Completed';
  cancellation_reason_id?: number;
}

export interface UpdateBookingStatusResponse {
  message: string;
  booking_id: number;
  booking_status: string;
}

export interface ServiceStatus {
  id: number;
  booking_id: number;
  status: 'not_started' | 'started' | 'completed' | 'payment_received';
  created_at: string;
  updated_at: string;
  started_at?: string;
  completed_at?: string;
  payment_received_at?: string;

  nurse_cognitoId?: string;
  customer_cognitoId?: string;
  customer_given_name?: string;
  nurse_given_name?: string;
  booked_date?: string;
  booked_slot?: string;
  booking_status?: string;
  hourly_fare?: string;
  services_selected?: string;
}

export interface ServiceStatusResponse {
  success: boolean;
  message: string;
  data: ServiceStatus | ServiceStatus[] | null;
}

export interface UpdateServiceStatusRequest {
  booking_id: number;
  status: 'not_started' | 'started' | 'completed' | 'payment_received';
}

export interface PopulateAcceptedBookingsResponse {
  success: boolean;
  message: string;
  data: {
    affectedRows: number;
  };
}

interface OTPVerificationData {
  username: string;
  confirmationCode: string;
}

interface Forgot_Password {
  phone_number: string;
}

interface ResetPasswordData {
  phone_number: string;
  confirmationCode: string;
  newPassword: string;
}

interface ChangePassword {
  phone_number: string;
  old_password: string;
  new_password: string;
}

interface ResendOTP {
  phone_number: string;
}

interface CustomerLocationData {
  latitude: number;
  longitude: number;
  address: string;
}

interface PhoneLoginData {
  phone_number: string;
  password: string;
}

interface GetProfileData {
  username: string;
}

interface PersonalDetailsData {
  service_provide: string;
  total_years_of_exp: number;
  relavant_years_of_exp: number;
  selected_services: string[];
  custom_service?: string;
  emergency_contact: string | '';
}

interface CustomerPersonalDetailsData {
  given_name: string;
  family_name: string;
  email: string;
}

interface ProfileDetails {
  id: number;
  user_id: string;
  created_at: string;
  updated_at: string;
  phone_number: string;
  given_name: string;
  family_name: string;
  email: string;
  longitude: number | '';
  latitude: number | '';
  address: string | '';
}

interface ProfileDetailsResponse {
  details: ProfileDetails;
}

export interface UserProfile {
  username: string;
  given_name: string;
  address: string;
  latitude: number;
  longitude: number;
  customer_set_location: boolean;
}

export interface AuthResponse {
  success: boolean;
  message?: string;
  tokens?: {
    accessToken: string;
    idToken?: string;
    refreshToken?: string;
  };
  user?: {
    id: string;
    cognito_id: string;
    email: string;
    given_name: string;
    middle_name?: string;
    family_name: string;
    username: string;
    customer_set_location: boolean;
    address: string;
    latitude: number;
    longitude: number;
  };
}

export interface AddressFormData {
  name: string;
  address: string;
  icon: 'home' | 'other';
  latitude?: number;
  longitude?: number;
}

export interface ValidationErrors {
  name?: string;
  address?: string;
  icon?: string;
}

interface ProfileResponse {
  customer_set_location: boolean;
  success: boolean;
  data?: {
    customer_set_location: boolean;
    address?: string;
    given_name?: string;
  };
}

export interface Address {
  id: number;
  name: string;
  address: string;
  icon: 'home' | 'other';
  created_at: string;
  updated_at: string;
}

export interface UpdateAddressData {
  name?: string;
  address?: string;
  icon?: 'home' | 'other';
}

export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message: string;
}

export interface ApiError {
  success: boolean;
  error: string;
  message: string;
}

export interface Address {
  id: number;
  name: string;
  address: string;
  icon: 'home' | 'other';
  latitude?: number;
  longitude?: number;
  created_at: string;
  updated_at: string;
}

interface DeleteAccount {
  phone_number: string;
  password: string;
}

export interface Nurse {
  id: string;
  nuid: string;
  cognito_id: string;
  given_name: string;
  family_name: string;
  latitude?: string;
  longitude?: string;
  address?: string;
  total_ratings?: string;
  average_rating?: string;
  distance?: string;
  hourly_fare?: number;
  service_provide?: string;
  total_years_of_exp?: string;
  about?: string;
  availability_slots?: string[];
  profile_image_signed_url?: string;
  s3_key?: string;
}

export interface ProximityNursesResponse {
  success: boolean;
  customerId: string;
  radius: number;
  totalNursesFound: number;
  averageRating: number;
  totalRatings: number;
  data: Nurse[];
  nurses: Nurse[];
  hourly_fare: number;
  available_Slots: string[];
  message: string;
}

export interface ProximityNursesParams {
  customerId: string;
  radius: number;
  addressId?: string;
}

export interface CancellationReason {
  id: number;
  cancellation_reasons: string;
  isActive: number;
  created_at: string;
  updated_at: string;
}

export interface StatusBadgeProps {
  bookingStatus: string;
  badgeColor: string;
}

export interface ActionDropdownProps {
  bookingStatus: string;
  showDropdown: boolean;
  setShowDropdown: (show: boolean) => void;
  dropdownRef: React.RefObject<HTMLDivElement>;
  onCancelClick: () => void;
}

export interface CancellationReasonProps {
  bookingStatus: string;
  cancellationReason?: string;
}

export interface MessageButtonProps {
  bookingStatus: string;
  unreadCount: number;
  onNavigate: () => void;
}

export interface ViewMoreButtonProps {
  bookingStatus: string;
  showTimeline: boolean;
  onToggleTimeline: () => void;
}

export interface PaymentButtonProps {
  bookingStatus: string;
  onNavigate: () => void;
}

export interface ServiceTimelineProps {
  bookingStatus: string;
  showTimeline: boolean;
  booking: UpcomingScheduleBooking;
  bookingServiceStatus?: ServiceStatus;
  onNavigateToReview: () => void;
}

export interface TimelineStepProps {
  title: string;
  description: string;
  timestamp?: string;
  isActive: boolean;
  showConnector: boolean;
  bgColor: string;
}

export interface ReviewSectionProps {
  onNavigate: () => void;
}

export interface UpcomingScheduleBooking {
  booking_id: number;
  booking_status: string;
  booked_date: string;
  booked_slot: string;
  nurse_given_name: string;
  nurse_family_name: string;
  nurse_cognitoId?: string;
  nurse_location_address?: string;
  cancellation_reason?: string;
  created_at?: string;
  nurse_profile_image_signed_url?: string;
  nurse_profile_s3_key?: string;
  profile_image_name?: string;
  services_selected?: string;
}

export interface UpcomingScheduleProps {
  booking: UpcomingScheduleBooking;
}

export const apiSlice = createApi({
  reducerPath: 'api',
  baseQuery: fetchBaseQuery({
    baseUrl,
    prepareHeaders: headers => {
      const token =
        localStorage.getItem('idToken') || localStorage.getItem('token');
      if (token) {
        headers.set('authorization', `Bearer ${token}`);
      }
      return headers;
    },
  }),
  tagTypes: [
    'Profile',
    'Feedback',
    'Document',
    'Address',
    'Nurses',
    'Bookings',
    'ServiceStatus',
  ],
  endpoints: builder => ({
    getExample: builder.query({
      query: () => 'example',
    }),

    registerCustomer: builder.mutation<AuthResponse, CustomerRegistrationData>({
      query: data => ({
        url: 'auth/register',
        method: 'POST',
        body: data,
      }),

      transformResponse: (response: AuthResponse) => {
        if (response?.tokens?.idToken) {
          localStorage.setItem('token', response.tokens.idToken);
        }
        return response;
      },
    }),

    verifyOTP: builder.mutation<AuthResponse, OTPVerificationData>({
      query: data => ({
        url: 'auth/confirm',
        method: 'POST',
        body: data,
      }),

      transformResponse: (response: AuthResponse) => {
        if (response?.tokens?.idToken) {
          localStorage.setItem('token', response.tokens.idToken);
        }
        return response;
      },
    }),

    forgotPassword: builder.mutation<AuthResponse, Forgot_Password>({
      query: data => ({
        url: 'auth/forgot-password',
        method: 'POST',
        body: data,
      }),
    }),

    confirmForgotPassword: builder.mutation<AuthResponse, ResetPasswordData>({
      query: data => ({
        url: 'auth/confirm-forgot-password',
        method: 'POST',
        body: data,
      }),
    }),

    changePassword: builder.mutation<AuthResponse, ChangePassword>({
      query: data => ({
        url: 'auth/change-password',
        method: 'POST',
        body: data,
      }),
    }),

    resendOTP: builder.mutation<AuthResponse, ResendOTP>({
      query: data => ({
        url: 'auth/resend-otp',
        method: 'POST',
        body: data,
      }),
    }),

    updateCustomerLocation: builder.mutation<
      AuthResponse,
      CustomerLocationData
    >({
      query: data => ({
        url: 'users/location',
        method: 'PUT',
        body: data,
      }),
      transformResponse: (response: AuthResponse) => {
        if (response?.tokens?.idToken) {
          localStorage.setItem('token', response.tokens.idToken);
        }
        return response;
      },
      invalidatesTags: ['Profile'],
    }),

    phoneLogin: builder.mutation<AuthResponse, PhoneLoginData>({
      query: data => ({
        url: 'auth/login/phone',
        method: 'POST',
        body: data,
      }),
      transformResponse: (response: AuthResponse) => {
        if (response?.tokens?.idToken) {
          localStorage.setItem('token', response.tokens.idToken);
        }
        return response;
      },
    }),
    getUsers: builder.query<UserProfile, GetProfileData>({
      query: () => '/users/profile',
      providesTags: ['Profile'],
    }),
    getProfile: builder.query<ProfileResponse, GetProfileData>({
      query: data => ({
        url: 'users/profile',
        method: 'POST',
        body: data,
      }),

      keepUnusedDataFor: 300,
    }),
    createPersonalDetails: builder.mutation<AuthResponse, PersonalDetailsData>({
      query: data => ({
        url: 'personal-details/create',
        method: 'POST',
        body: data,
      }),
    }),

    getProfileDetails: builder.query<ProfileDetailsResponse, void>({
      query: () => ({
        url: 'personal-details/getProfile',
        method: 'GET',
      }),
    }),

    updateCustomerPersonalDetails: builder.mutation<
      AuthResponse,
      CustomerPersonalDetailsData
    >({
      query: data => ({
        url: 'personal-details/updatePersonalProfile',
        method: 'PUT',
        body: data,
      }),
    }),

    getAddressById: builder.query<ApiResponse<Address>, number>({
      query: id => `/addresses/${id}`,
      providesTags: (result, error, id) => [{ type: 'Address', id }],
    }),

    getAddresses: builder.query<ApiResponse<Address[]>, void>({
      query: () => '/addresses/search',
      providesTags: ['Address'],
    }),

    createAddress: builder.mutation<ApiResponse<Address>, AddressFormData>({
      query: data => ({
        url: '/addresses/create',
        method: 'POST',
        body: data,
      }),
      invalidatesTags: ['Address'],
    }),

    updateAddress: builder.mutation<
      ApiResponse<Address>,
      { id: number; data: AddressFormData }
    >({
      query: ({ id, data }) => ({
        url: `/addresses/update/${id}`,
        method: 'PUT',
        body: data,
      }),
      invalidatesTags: ['Address'],
    }),

    deleteAddress: builder.mutation<ApiResponse<void>, number>({
      query: id => ({
        url: `/addresses/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['Address'],
    }),
    deleteAccount: builder.mutation<AuthResponse, DeleteAccount>({
      query: data => ({
        url: 'auth/delete-account',
        method: 'POST',
        body: data,
      }),
    }),

    getProximityNurses: builder.query<
      ProximityNursesResponse,
      ProximityNursesParams
    >({
      query: params => ({
        url: 'proximity/nurses',
        method: 'GET',
        params: {
          customerId: params.customerId,
          radius: params.radius,
          ...(params.addressId ? { addressId: params.addressId } : {}),
        },
      }),
      providesTags: ['Nurses'],
    }),

    createBooking: builder.mutation<createBookingResponse, CreateBookingData>({
      query: data => ({
        url: '/bookings/create-booking',
        method: 'POST',
        body: data,
      }),
    }),

    getBookingsByCustomer: builder.query<GetBookingsByCustomerResponse, string>(
      {
        query: customer_cognitoId => ({
          url: `/bookings/customer/${customer_cognitoId}`,
          method: 'GET',
        }),
        providesTags: ['Bookings'],
      }
    ),

    updateBookingStatus: builder.mutation<
      UpdateBookingStatusResponse,
      UpdateBookingStatusRequest
    >({
      query: ({ booking_id, booking_status, cancellation_reason_id }) => ({
        url: `/bookings/${booking_id}/status`,
        method: 'PUT',
        body: { booking_status, cancellation_reason_id },
      }),
      invalidatesTags: ['Bookings', 'ServiceStatus'],
    }),

    getServiceStatusByBookingId: builder.query<ServiceStatusResponse, number>({
      query: bookingId => ({
        url: `/service-status/booking/${bookingId}`,
        method: 'GET',
      }),
      providesTags: (result, error, bookingId) => [
        { type: 'ServiceStatus', id: bookingId },
        'ServiceStatus',
      ],
    }),

    updateServiceStatus: builder.mutation<
      ServiceStatusResponse,
      UpdateServiceStatusRequest
    >({
      query: ({ booking_id, status }) => ({
        url: `/service-status/booking/${booking_id}`,
        method: 'PUT',
        body: { status },
      }),
      invalidatesTags: (result, error, { booking_id }) => [
        { type: 'ServiceStatus', id: booking_id },
        'ServiceStatus',
      ],
    }),

    getNurseServiceStatuses: builder.query<ServiceStatusResponse, string>({
      query: nurseCognitoId => ({
        url: `/service-status/nurse/${nurseCognitoId}`,
        method: 'GET',
      }),
      providesTags: (result, error, nurseCognitoId) => [
        { type: 'ServiceStatus', id: `nurse-${nurseCognitoId}` },
        'ServiceStatus',
      ],
    }),

    getCustomerServiceStatuses: builder.query<ServiceStatusResponse, string>({
      query: customerCognitoId => ({
        url: `/service-status/customer/${customerCognitoId}`,
        method: 'GET',
      }),
      providesTags: (result, error, customerCognitoId) => [
        { type: 'ServiceStatus', id: `customer-${customerCognitoId}` },
        'ServiceStatus',
      ],
    }),

    populateAcceptedBookings: builder.mutation<
      PopulateAcceptedBookingsResponse,
      void
    >({
      query: () => ({
        url: `/service-status/populate-accepted`,
        method: 'POST',
      }),
      invalidatesTags: ['ServiceStatus'],
    }),

    getCancellationReasons: builder.query<CancellationReason[], void>({
      query: () => '/cancellation-reasons',
      transformResponse: (response: { data?: CancellationReason[] }) => {
        return response?.data || [];
      },
    }),

    clearCache: builder.mutation<void, void>({
      queryFn: () => ({ data: undefined }),
      invalidatesTags: [
        'Profile',
        'Feedback',
        'Document',
        'Address',
        'Nurses',
        'Bookings',
        'ServiceStatus',
      ],
    }),
  }),
});

export const {
  useGetExampleQuery,
  useGetUsersQuery,
  useRegisterCustomerMutation,
  useVerifyOTPMutation,
  useForgotPasswordMutation,
  useConfirmForgotPasswordMutation,
  useResendOTPMutation,
  useChangePasswordMutation,
  useDeleteAccountMutation,
  useUpdateCustomerLocationMutation,
  usePhoneLoginMutation,
  useGetProfileQuery,
  useCreatePersonalDetailsMutation,
  useGetProfileDetailsQuery,
  useUpdateCustomerPersonalDetailsMutation,
  useGetAddressesQuery,
  useGetAddressByIdQuery,
  useCreateAddressMutation,
  useUpdateAddressMutation,
  useDeleteAddressMutation,
  useGetProximityNursesQuery,
  useCreateBookingMutation,
  useGetBookingsByCustomerQuery,
  useUpdateBookingStatusMutation,
  useGetServiceStatusByBookingIdQuery,
  useUpdateServiceStatusMutation,
  useGetNurseServiceStatusesQuery,
  useGetCustomerServiceStatusesQuery,
  usePopulateAcceptedBookingsMutation,
  useGetCancellationReasonsQuery,
  useClearCacheMutation,
} = apiSlice;
