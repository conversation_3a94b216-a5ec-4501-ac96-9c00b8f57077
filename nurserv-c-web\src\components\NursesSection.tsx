import React, { useState } from 'react';
import { Nurse, ProximityNursesResponse, Address } from '@/store/api/apiSlice';
import { FetchBaseQueryError } from '@reduxjs/toolkit/query';
import { SerializedError } from '@reduxjs/toolkit';
import ResponsiveLoader from '@/components/Loader';
import AboutNurse from '@/pages/AboutNurse';
import Stack from '@mui/material/Stack';
import Pagination from '@mui/material/Pagination';

interface NursesSectionProps {
  nursesResponse: ProximityNursesResponse | null;
  nursesLoading: boolean;
  nursesError: FetchBaseQueryError | SerializedError | null;
  selectedRadius: number;
  onRadiusChange: (radius: number) => void;
  customerId: string;
  customerName: string;
  selectedAddress: Address | null;
}

const NursesSection: React.FC<NursesSectionProps> = ({
  nursesResponse,
  nursesLoading,
  nursesError,
  selectedRadius,
  onRadiusChange,
  customerId,
  customerName,
  selectedAddress,
}) => {
  const [nursesPage, setNursesPage] = useState(1);
  const nursesPerPage = 5;

  const renderRadiusButtons = () => {
    return [2, 3, 5, 7].map(radius => (
      <button
        key={radius}
        className={`md:px-3 md:py-1 px-[10px] py-[4px] rounded-full shadow-md text-white transition-colors duration-300 ${
          selectedRadius === radius
            ? 'border-[#f09e22] bg-[#f09e22] text-white font-bold'
            : 'bg-nursery-blue'
        }`}
        onClick={() => onRadiusChange(radius)}
      >
        {radius} km
      </button>
    ));
  };

  const renderNursesList = () => {
    if (!nursesResponse?.nurses || nursesResponse.nurses.length === 0) {
      return (
        <div className='bg-[#F2F2F2] p-7 items-center flex flex-1 flex-col justify-center rounded-xl gap-2'>
          <p className='text-base font-medium text-slate-500'>
            No nurses found nearby
          </p>
          <p className='text-sm text-slate-400'>
            Try increasing the search radius or check back later
          </p>
        </div>
      );
    }

    const startIdx = (nursesPage - 1) * nursesPerPage;
    const endIdx = startIdx + nursesPerPage;
    const paginatedNurses = nursesResponse.nurses.slice(startIdx, endIdx);

    return (
      <div className='space-y-4'>
        {paginatedNurses.map((nurse: Nurse, index: number) => (
          <div
            key={nurse.id || index}
            className='bg-[#F2F2F2] p-4 rounded-xl shadow flex flex-col'
          >
            <AboutNurse
              nurse={{
                name: `${nurse.given_name} ${nurse.family_name}`,
                rating: nurse.average_rating || '0',
                location: `${nurse.distance || '0'} Km - ${
                  nurse.address || 'Location not available'
                }`,
                latitude: nurse.latitude,
                longitude: nurse.longitude,
                fees: nurse.hourly_fare,
                about: nurse.about,
                total_years_of_exp: nurse.total_years_of_exp,
                nurse_id: nurse.cognito_id,
                nuid: nurse.nuid,
                services: nurse.service_provide
                  .split(',')
                  .map((service: string) => service.trim()),
                available_slots: nurse.availability_slots,
                customerAddress: selectedAddress?.address || '',
                customerLatitude: selectedAddress?.latitude || 0,
                customerLongitude: selectedAddress?.longitude || 0,
                customerId: customerId,
                customerName: customerName,
                profile_image_signed_url: nurse.profile_image_signed_url,
              }}
            />
          </div>
        ))}
        <div className='flex justify-center pt-4'>
          <Stack spacing={2}>
            <Pagination
              count={Math.ceil(nursesResponse.nurses.length / nursesPerPage)}
              page={nursesPage}
              onChange={(_, value) => setNursesPage(value)}
              siblingCount={0}
              boundaryCount={1}
            />
          </Stack>
        </div>
      </div>
    );
  };

  const renderNursesContent = () => {
    if (nursesLoading) {
      return (
        <div className='bg-[#F2F2F2] p-7 items-center flex flex-1 flex-col justify-center rounded-xl gap-2'>
          <ResponsiveLoader />
          <p className='text-base font-medium text-gray-500'>
            Loading nearby nurses...
          </p>
        </div>
      );
    }

    if (nursesError) {
      return (
        <div className='bg-red-50 p-7 items-center flex flex-1 flex-col justify-center rounded-xl gap-2'>
          <p className='text-base font-medium text-red-500'>
            Failed to load nearby nurses
          </p>
          <p className='text-sm text-red-400'>Please try again later</p>
        </div>
      );
    }

    return renderNursesList();
  };

  return (
    <div className='md:pt-3 pt-5 max-w-full'>
      <div className='flex flex-1 sm:flex-row flex-col justify-between'>
        <h3 className='text-xl font-semibold text-gray-800 mb-3'>
          Nurses Nearby
        </h3>
        <div className='flex justify-evenly items-center md:gap-10 gap-5 mb-4 p-1 rounded-full bg-white'>
          {renderRadiusButtons()}
        </div>
      </div>
      {renderNursesContent()}
    </div>
  );
};

export default NursesSection;
