const WebSocket = require('ws');
const { verifyAndGetUser, validateAndRefreshSession, extractToken } = require('./authUtils');
const Conversation = require('../models/Conversation');
const Message = require('../models/Message');

class WebSocketManager {
  constructor(server) {
    this.wss = new WebSocket.Server({ 
      server,
      // Add more verbose error handling for upgrade failures
      verifyClient: (info, cb) => {
        // Always accept at this stage, we'll authenticate after upgrade
        cb(true);
      }
    });
    this.clients = new Map(); // Map to store connected clients
    this.rooms = new Map(); // Map to store room participants
    
    this.initialize();
  }

  initialize() {
    this.wss.on('connection', (ws, req) => {
      this.handleConnection(ws, req);
    });

    // Listen for server errors
    this.wss.on('error', (error) => {
      console.error('WebSocket server error:', error);
    });

    // Listen for headers errors (happens before upgrade)
    this.wss.on('headers', (headers, req) => {
      console.debug(`WebSocket headers for ${req.socket.remoteAddress}: ${headers.length} headers`);
    });

  }

  async handleConnection(ws, req) {
    // Generate a unique connection ID for tracking this connection in logs
    const connectionId = Math.random().toString(36).substring(2, 10);
    const clientIp = req.headers['x-forwarded-for'] || req.socket.remoteAddress;
      
    // Set a timeout to close the connection if authentication takes too long
    const authTimeout = setTimeout(() => {
      console.warn(`[${connectionId}] Authentication timeout after 10s`);
      try {
        ws.close(1008, 'Authentication timeout');
      } catch (err) {
        console.error(`[${connectionId}] Error closing timed-out connection:`, err);
      }
    }, 10000);
    
    try {
      // Extract token from query string or headers
      const token = extractToken(req);
      
      if (!token) {
        console.warn(`[${connectionId}] No authentication token provided`);
        ws.close(1008, 'Authentication required');
        clearTimeout(authTimeout);
        return;
      }
      // Parse URL parameters for additional context
      const url = new URL(req.url, `http://${req.headers.host}`);
      const urlUserId = url.searchParams.get('userId');
      const urlUserType = url.searchParams.get('userType');
      
      // ------------------------------------------------------------------
      // Verify JWT token against Cognito user pools (patient & nurse)
      // ------------------------------------------------------------------
      let verifiedToken, userType, userId, email, username;

      try {
        const authResult = await verifyAndGetUser(token);
        verifiedToken = authResult.verifiedToken;
        userType = authResult.userType;
        userId = authResult.userId;
        email = authResult.email;
        username = authResult.username;
      } catch (authError) {
        console.error(`[${connectionId}] Token verification failed:`, authError);
        ws.close(1008, 'Authentication failed');
        clearTimeout(authTimeout);
        return;
      }

      // Validate URL parameters match token claims
      if (urlUserId && urlUserId !== userId) {
        console.warn(`[${connectionId}] URL userId (${urlUserId}) doesn't match token subject (${userId})`);
        // We'll continue anyway but log the discrepancy
      }

      // Check if user session is valid
      let sessionValid;
      try {
        sessionValid = await validateAndRefreshSession(userId, userType);
      } catch (sessionError) {
        console.error(`[${connectionId}] Session validation error:`, sessionError);
        sessionValid = process.env.NODE_ENV === 'development'; // Allow in development
      }

      if (!sessionValid) {
        console.warn(`[${connectionId}] Invalid session for ${userId} (${userType})`);
        ws.close(1008, 'Session expired');
        clearTimeout(authTimeout);
        return;
      }
      
      clearTimeout(authTimeout);

      // Store client information
      const clientInfo = {
        connectionId,
        userId,
        userType,
        email,
        username,
        ws,
        rooms: new Set(),
        connectedAt: new Date(),
        lastActivity: Date.now()
      };

      // Check if user already has an active connection
      if (this.clients.has(userId)) {
        const existingClient = this.clients.get(userId);
        try {
          // Close the existing connection with a replacement code
          existingClient.ws.close(1001, 'Connection replaced by newer session');
        } catch (err) {
          console.error(`[${connectionId}] Error closing previous connection:`, err);
        }
      }

      this.clients.set(userId, clientInfo);
      // Send welcome message
      try {
        ws.send(JSON.stringify({
          type: 'CONNECTION_SUCCESS',
          status: 'connected',
          userId,
          userType,
          email,
          username,
          connectionId
        }));
      } catch (err) {
        console.error(`[${connectionId}] Error sending welcome message:`, err);
      }

      // Handle incoming messages
      ws.on('message', async (data) => {
        try {
          // Update last activity timestamp
          clientInfo.lastActivity = Date.now();
          await this.handleMessage(clientInfo, data);
        } catch (err) {
          console.error(`[${connectionId}] Error in message handler:`, err);
        }
      });

      // Handle client disconnect
      ws.on('close', (code, reason) => {
        this.handleDisconnect(clientInfo);
      });

      // Handle errors
      ws.on('error', (error) => {
        console.error(`[${connectionId}] WebSocket error:`, error);
        this.handleDisconnect(clientInfo);
      });

      // Set up ping interval to keep connection alive
      const pingInterval = setInterval(() => {
        if (ws.readyState === WebSocket.OPEN) {
          try {
            // Send a ping message every 30 seconds
            ws.ping();
          } catch (err) {
            console.error(`[${connectionId}] Error sending ping:`, err);
            clearInterval(pingInterval);
          }
        } else {
          // Clear interval if socket is closed
          clearInterval(pingInterval);
        }
      }, 30000);

      // Clean up ping interval on close
      ws.on('close', () => {
        clearInterval(pingInterval);
      });

    } catch (error) {
      console.error(`[${connectionId}] WebSocket connection error:`, error);
      
      // Send a more detailed error message to the client
      try {
        const errorMessage = {
          type: 'CONNECTION_ERROR',
          message: 'Authentication failed',
          details: process.env.NODE_ENV === 'development' ? error.message : 'Connection error'
        };
        
        ws.send(JSON.stringify(errorMessage));
        
        // Close with appropriate code
        let closeCode = 1008; // Policy violation (auth failure)
        let closeReason = 'Authentication failed';
        
        if (error.message.includes('token')) {
          closeReason = 'Invalid token';
        } else if (error.message.includes('session')) {
          closeReason = 'Session expired';
        } else if (error.message.includes('JWKS')) {
          closeCode = 1011; // Internal server error
          closeReason = 'Token verification service unavailable';
        }
        
        ws.close(closeCode, closeReason);
      } catch (sendError) {
        console.error(`[${connectionId}] Error sending error message:`, sendError);
        try {
          ws.close(1011, 'Internal server error');
        } catch (closeError) {
          console.error(`[${connectionId}] Error closing connection:`, closeError);
        }
      }
      
      clearTimeout(authTimeout);
    }
  }

  async handleMessage(clientInfo, data) {
    const { connectionId, userId } = clientInfo;
    
    try {
      // Handle ping/pong messages to keep connection alive
      if (data.toString() === 'ping' || data.toString() === '{"type":"PING"}') {
        clientInfo.ws.send(JSON.stringify({ type: 'PONG', timestamp: Date.now() }));
        return;
      }
      
      // Parse JSON message
      const message = JSON.parse(data);      
      switch (message.type) {
        /* ------------ JOIN / LEAVE ------------ */
        case 'JOIN_CONVERSATION':
        case 'join_room': // legacy
          this.joinRoom(clientInfo, message.conversationId);
          break;
        case 'LEAVE_CONVERSATION':
        case 'leave_room': // legacy
          this.leaveRoom(clientInfo, message.conversationId);
          break;

        /* ------------ TEXT MESSAGE ------------ */
        case 'TEXT_MESSAGE':
        case 'send_message': // legacy
          await this.broadcastMessage(clientInfo, message);
          break;

        /* ------------ TYPING ------------------ */
        case 'TYPING_INDICATOR':
        case 'typing': // legacy
          this.broadcastTyping(clientInfo, message);
          break;

        /* ------------ READ RECEIPT ------------ */
        case 'READ_RECEIPT':
        case 'read_receipt': // legacy
          await this.handleReadReceipt(clientInfo, message);
          break;

        /* ------------ CONVERSATION MANAGEMENT ------------ */
        case 'GET_CONVERSATIONS':
          this.handleGetConversations(clientInfo, message);
          break;

        case 'GET_CONVERSATION':
          this.handleGetConversation(clientInfo, message);
          break;

        case 'CREATE_CONVERSATION':
          this.handleCreateConversation(clientInfo, message);
          break;

        case 'GET_MESSAGES':
          this.handleGetMessages(clientInfo, message);
          break;

        case 'SEARCH_MESSAGES':
          this.handleSearchMessages(clientInfo, message);
          break;

        case 'MARK_MESSAGES_READ':
          this.handleMarkMessagesRead(clientInfo, message);
          break;

        case 'UPDATE_CONVERSATION_STATUS':
          this.handleUpdateConversationStatus(clientInfo, message);
          break;

        case 'GET_UNREAD_COUNT':
          this.handleGetUnreadCount(clientInfo, message);
          break;

        case 'GET_NURSE_INFO':
          this.handleGetNurseInfo(clientInfo, message);
          break;

        case 'PING':
          // Client-side ping, respond with pong
          clientInfo.ws.send(JSON.stringify({ type: 'PONG', timestamp: Date.now() }));
          break;

        default:
          console.warn(`[${connectionId}] Unknown message type from ${userId}: ${message.type}`);
          // Send error back to client
          try {
            clientInfo.ws.send(JSON.stringify({
              type: 'ERROR',
              error: `Unknown message type: ${message.type}`,
              timestamp: Date.now()
            }));
          } catch (err) {
            console.error(`[${connectionId}] Error sending error message:`, err);
          }
      }
    } catch (error) {
      console.error(`[${connectionId}] Error handling WebSocket message:`, error);
      // Try to send error back to client
      try {
        clientInfo.ws.send(JSON.stringify({
          type: 'ERROR',
          error: 'Failed to process message',
          details: process.env.NODE_ENV === 'development' ? error.message : 'Message processing error',
          timestamp: Date.now()
        }));
      } catch (sendError) {
        console.error(`[${connectionId}] Error sending error message:`, sendError);
      }
    }
  }

  joinRoom(clientInfo, conversationId) {
    const { connectionId, userId } = clientInfo;
    
    if (!conversationId) {
      console.error(`[${connectionId}] Invalid conversationId for join room`);
      return;
    }
    
    if (!this.rooms.has(conversationId)) {
      this.rooms.set(conversationId, new Set());
    }
    
    this.rooms.get(conversationId).add(userId);
    clientInfo.rooms.add(conversationId);
        
    // Notify other participants
    this.broadcastToRoom(conversationId, {
      type: 'USER_JOINED',
      userId: clientInfo.userId,
      userType: clientInfo.userType,
      conversationId: conversationId,
      timestamp: new Date().toISOString()
    }, clientInfo.userId);
    
    // Confirm to the user that they joined
    try {
      clientInfo.ws.send(JSON.stringify({
        type: 'JOIN_CONFIRMATION',
        conversationId,
        timestamp: new Date().toISOString(),
        participantCount: this.rooms.get(conversationId).size
      }));
    } catch (err) {
      console.error(`[${connectionId}] Error sending join confirmation:`, err);
    }
  }

  leaveRoom(clientInfo, conversationId) {
    const { connectionId, userId } = clientInfo;
    
    if (!conversationId) {
      console.error(`[${connectionId}] Invalid conversationId for leave room`);
      return;
    }
    
    if (this.rooms.has(conversationId)) {
      this.rooms.get(conversationId).delete(userId);
      
      if (this.rooms.get(conversationId).size === 0) {
        this.rooms.delete(conversationId);
      }
    }
    
    clientInfo.rooms.delete(conversationId);
    
    // Notify other participants
    this.broadcastToRoom(conversationId, {
      type: 'USER_LEFT',
      userId: clientInfo.userId,
      userType: clientInfo.userType,
      conversationId: conversationId,
      timestamp: new Date().toISOString()
    }, clientInfo.userId);
  }

  async broadcastMessage(clientInfo, message) {
    const { connectionId, userId } = clientInfo;
    const { conversationId, content, messageType = 'text' } = message;
    try {
      // First, save the message to the database
      const Message = require('../models/Message');
      const savedMessage = await Message.sendMessage(
        conversationId,
        userId,
        clientInfo.userType,
        content,
        messageType
      );
      // Create WebSocket message with the saved message data
      const wsMessage = {
        type: 'TEXT_MESSAGE',
        conversationId: conversationId,
        senderId: userId,
        senderType: clientInfo.userType,
        senderName: clientInfo.userType === 'nurse' ? 'Nurse' : 'Patient', // TODO: Get actual names
        content: content,
        messageType: messageType,
        messageId: savedMessage._id.toString(),
        timestamp: savedMessage.createdAt ? savedMessage.createdAt.toISOString() : new Date().toISOString(),
        status: savedMessage.status // Include the message status
      };

      // Send confirmation back to sender first
      try {
        clientInfo.ws.send(JSON.stringify({
          type: 'MESSAGE_SENT',
          success: true,
          messageId: savedMessage._id.toString(),
          conversationId: conversationId,
          timestamp: wsMessage.timestamp
        }));
      } catch (sendError) {
        console.error(`[${connectionId}] Error sending confirmation to sender:`, sendError);
      }

      // Get room participants to determine delivery status
      const participants = this.rooms.get(conversationId);
      // Broadcast to other participants (exclude the sender)
      const recipientCount = this.broadcastToRoom(conversationId, wsMessage, userId);
      // If message was successfully delivered to at least one recipient, mark as delivered
      if (recipientCount > 0) {
        // Update message status to 'delivered' since there are active recipients who received the message
        await Message.findByIdAndUpdate(savedMessage._id, {
          status: 'delivered'
        });

        // Send delivery confirmation to sender after a small delay to ensure MESSAGE_SENT is processed first
        setTimeout(() => {
          try {
            clientInfo.ws.send(JSON.stringify({
              type: 'MESSAGE_STATUS_UPDATE',
              messageId: savedMessage._id.toString(),
              conversationId: conversationId,
              status: 'delivered',
              timestamp: new Date().toISOString()
            }));
          } catch (sendError) {
            console.error(`[${connectionId}] Error sending delivery status to sender:`, sendError);
          }
        }, 50); // 50ms delay to ensure proper order
      }
    } catch (error) {
      console.error(`[${connectionId}] Error saving/broadcasting message:`, error);

      // Send error back to sender
      try {
        clientInfo.ws.send(JSON.stringify({
          type: 'MESSAGE_ERROR',
          success: false,
          error: 'Failed to send message',
          conversationId: conversationId,
          timestamp: new Date().toISOString()
        }));
      } catch (sendError) {
        console.error(`[${connectionId}] Error sending error message:`, sendError);
      }
    }
  }

  broadcastTyping(clientInfo, message) {
    const { connectionId, userId } = clientInfo;
    const { conversationId, isTyping } = message;
    
    if (!conversationId) {
      console.error(`[${connectionId}] Missing conversationId in typing indicator`);
      return;
    }
    
    this.broadcastToRoom(conversationId, {
      type: 'TYPING_INDICATOR',
      conversationId,
      senderId: userId,
      senderType: clientInfo.userType,
      senderName: clientInfo.username,
      isTyping: isTyping !== false,
      timestamp: new Date().toISOString()
    }, userId);
  }

  async handleReadReceipt(clientInfo, message) {
    const { connectionId, userId } = clientInfo;
    const { conversationId, messageId } = message;

    if (!conversationId) {
      console.error(`[${connectionId}] Missing conversationId in read receipt`);
      return;
    }

    try {
      const Message = require('../models/Message');

      // If messageId is provided, mark specific message as read
      // If not provided, mark all unread messages in conversation as read
      if (messageId) {
        // Update the specific message status to 'read' in MongoDB
        const updateResult = await Message.findByIdAndUpdate(
          messageId,
          {
            status: 'read',
            readAt: new Date()
          },
          { new: true }
        );

        if (updateResult) {
          // Send MESSAGE_STATUS_UPDATE to the original sender
          const originalSenderId = updateResult.senderId;
          if (originalSenderId && originalSenderId !== userId) {
            this.sendToUser(originalSenderId, {
              type: 'MESSAGE_STATUS_UPDATE',
              messageId: messageId,
              conversationId: conversationId,
              status: 'read',
              readBy: userId,
              readByName: clientInfo.username,
              readByType: clientInfo.userType,
              timestamp: new Date().toISOString()
            });
          }
        } else {
          console.warn(`[${connectionId}] Message ${messageId} not found for read receipt`);
        }

        // Broadcast specific message read receipt (exclude the reader)
        this.broadcastToRoom(conversationId, {
          type: 'READ_RECEIPT',
          conversationId,
          messageId,
          readBy: userId,
          readByName: clientInfo.username,
          readByType: clientInfo.userType,
          readAt: new Date().toISOString()
        }, userId);
      } else {
        // Get the messages that will be marked as read before updating them
        const messagesToUpdate = await Message.find({
          conversationId,
          senderId: { $ne: userId },
          status: { $ne: 'read' }
        }).select('_id senderId');

        // Mark all unread messages in the conversation as read
        const result = await Message.markAsRead(conversationId, userId);
        // Send MESSAGE_STATUS_UPDATE to each original sender
        if (messagesToUpdate.length > 0) {
          const senderIds = [...new Set(messagesToUpdate.map(msg => msg.senderId))];
          senderIds.forEach(senderId => {
            if (senderId !== userId) {
              // Get all message IDs for this sender
              const senderMessageIds = messagesToUpdate
                .filter(msg => msg.senderId === senderId)
                .map(msg => msg._id.toString());

              // Send status update for each message
              senderMessageIds.forEach(messageId => {
                this.sendToUser(senderId, {
                  type: 'MESSAGE_STATUS_UPDATE',
                  messageId: messageId,
                  conversationId: conversationId,
                  status: 'read',
                  readBy: userId,
                  readByName: clientInfo.username,
                  readByType: clientInfo.userType,
                  timestamp: new Date().toISOString()
                });
              });
            }
          });
        }

        // Broadcast conversation read receipt (all messages read) (exclude the reader)
        this.broadcastToRoom(conversationId, {
          type: 'CONVERSATION_READ',
          conversationId,
          readBy: userId,
          readByName: clientInfo.username,
          readByType: clientInfo.userType,
          messagesMarked: result.modifiedCount,
          readAt: new Date().toISOString()
        }, userId);
      }
    } catch (error) {
      console.error(`[${connectionId}] Error handling read receipt:`, error);
    }
  }

  broadcastToRoom(conversationId, message, excludeUserId = null) {
    if (!this.rooms.has(conversationId)) {
      console.warn(`Room ${conversationId} not found for broadcasting message`);
      return 0;
    }

    const participants = this.rooms.get(conversationId);
    let sentCount = 0;
    participants.forEach(userId => {
      if (userId === excludeUserId) {
        return;
      }

      const client = this.clients.get(userId);
      if (client && client.ws.readyState === WebSocket.OPEN) {
        try {
          client.ws.send(JSON.stringify(message));
          sentCount++;
        } catch (err) {
          console.error(`[${client.connectionId}] Error sending to ${userId}:`, err);
        }
      } else {
        console.warn(`Client ${userId} not found or connection not open for room ${conversationId}`);
      }
    });

    return sentCount;
  }

  handleDisconnect(clientInfo) {
    if (!clientInfo) return;
    
    const { connectionId, userId } = clientInfo;    
    // Remove from all rooms
    clientInfo.rooms.forEach(conversationId => {
      this.leaveRoom(clientInfo, conversationId);
    });
    
    // Remove from clients map only if this is the current connection
    // (not if it was already replaced by a newer one)
    const currentClient = this.clients.get(userId);
    if (currentClient && currentClient.connectionId === connectionId) {
      this.clients.delete(userId);
    }    
  }

  // Send message to specific user (for one-on-one communication)
  sendToUser(userId, message) {
    const client = this.clients.get(userId);
    if (client && client.ws.readyState === WebSocket.OPEN) {
      try {
        client.ws.send(JSON.stringify(message));
        return true;
      } catch (error) {
        console.error(`Error sending message to user ${userId}:`, error);
        return false;
      }
    } else {
      return false;
    }
  }

  // Broadcast to all connected clients
  broadcastToAll(message) {
    let sentCount = 0;
    this.clients.forEach(client => {
      if (client.ws.readyState === WebSocket.OPEN) {
        try {
          client.ws.send(JSON.stringify(message));
          sentCount++;
        } catch (err) {
          console.error(`[${client.connectionId}] Error broadcasting to ${client.userId}:`, err);
        }
      }
    });
    return sentCount;
  }

  // Get connected clients count
  getConnectedClientsCount() {
    return this.clients.size;
  }

  // Get room participants count
  getRoomParticipantsCount(conversationId) {
    return this.rooms.has(conversationId) ? this.rooms.get(conversationId).size : 0;
  }

  // Get user's active conversations
  getUserActiveConversations(userId) {
    const client = this.clients.get(userId);
    return client ? Array.from(client.rooms) : [];
  }
  
  // Get server stats
  getStats() {
    return {
      totalClients: this.clients.size,
      totalRooms: this.rooms.size,
      rooms: Array.from(this.rooms.entries()).map(([roomId, participants]) => ({
        roomId,
        participantCount: participants.size,
        participants: Array.from(participants)
      }))
    };
  }

  // New handler methods for conversation management
  async handleGetConversations(clientInfo, message) {
    const { connectionId, userId, userType } = clientInfo;
    const { requestId, page = 1, limit = 20, status } = message;

    try {
      const conversations = await Conversation.getUserConversations(
        userId,
        userType,
        parseInt(page),
        parseInt(limit)
      );

      // Get unread counts for each conversation
      const conversationsWithUnreadCounts = await Promise.all(
        conversations.map(async (conversation) => {
          const unreadCount = await Message.getUnreadCountForUser(conversation._id.toString(), userId);
          return {
            ...conversation,
            unreadCount
          };
        })
      );

      // Filter by status if provided
      const filteredConversations = status
        ? conversationsWithUnreadCounts.filter(conv => conv.status === status)
        : conversationsWithUnreadCounts;

      const response = {
        type: 'CONVERSATIONS_RESPONSE',
        requestId,
        success: true,
        data: {
          conversations: filteredConversations,
          pagination: {
            page: parseInt(page),
            limit: parseInt(limit),
            total: filteredConversations.length,
            totalPages: Math.ceil(filteredConversations.length / parseInt(limit))
          }
        },
        timestamp: new Date().toISOString()
      };

      clientInfo.ws.send(JSON.stringify(response));
    } catch (error) {
      console.error(`[${connectionId}] Error getting conversations for ${userId}:`, error);
      const errorResponse = {
        type: 'CONVERSATIONS_RESPONSE',
        requestId,
        success: false,
        error: 'Failed to fetch conversations',
        timestamp: new Date().toISOString()
      };
      clientInfo.ws.send(JSON.stringify(errorResponse));
    }
  }

  async handleGetConversation(clientInfo, message) {
    const { connectionId, userId, userType } = clientInfo;
    const { requestId, conversationId } = message;

    try {
      const conversation = await Conversation.findById(conversationId);

      if (!conversation) {
        throw new Error('Conversation not found');
      }

      // Check if user has access to this conversation
      const hasAccess = (userType === 'patient' && conversation.customerId === userId) ||
                       (userType === 'nurse' && conversation.nurseId === userId);

      if (!hasAccess) {
        throw new Error('Access denied');
      }

      const response = {
        type: 'CONVERSATION_RESPONSE',
        requestId,
        success: true,
        data: {
          conversation
        },
        timestamp: new Date().toISOString()
      };

      clientInfo.ws.send(JSON.stringify(response));
    } catch (error) {
      console.error(`[${connectionId}] Error getting conversation for ${userId}:`, error);
      const errorResponse = {
        type: 'CONVERSATION_RESPONSE',
        requestId,
        success: false,
        error: error.message || 'Failed to fetch conversation',
        timestamp: new Date().toISOString()
      };
      clientInfo.ws.send(JSON.stringify(errorResponse));
    }
  }

  async handleCreateConversation(clientInfo, message) {
    const { connectionId, userId, userType } = clientInfo;
    const { requestId, nurseId, customerId, patientId, nurseName, initialMessage } = message;
    try {
      // Both patients and nurses can create conversations
      if (userType !== 'patient' && userType !== 'nurse') {
        console.error(`🔴 [${connectionId}] User ${userId} has invalid user type (${userType}), cannot create conversation`);
        throw new Error('Only patients and nurses can create conversations');
      }

      // Determine the final customerId and nurseId based on user type
      let finalCustomerId, finalNurseId;

      if (userType === 'patient') {
        // Patient creating conversation with nurse
        if (!nurseId) {
          throw new Error('Nurse ID is required when patient creates conversation');
        }
        finalCustomerId = userId;
        finalNurseId = nurseId;
      } else if (userType === 'nurse') {
        // Nurse creating conversation with patient
        // Handle both customerId and patientId (frontend might send either)
        const targetPatientId = customerId || patientId;
        if (!targetPatientId) {
          throw new Error('Patient ID is required when nurse creates conversation');
        }
        finalCustomerId = targetPatientId;
        finalNurseId = userId;
      }

      // Check if conversation already exists
      let conversation = await Conversation.findOne({
        customerId: finalCustomerId,
        nurseId: finalNurseId
      });

      if (conversation) {
        // Return existing conversation
        const response = {
          type: 'CONVERSATION_CREATED',
          requestId,
          success: true,
          data: {
            conversation,
            isNew: false
          },
          timestamp: new Date().toISOString()
        };
        clientInfo.ws.send(JSON.stringify(response));
        return;
      }

      // Create new conversation
      conversation = await Conversation.createConversation(finalCustomerId, finalNurseId, 'Chat Session');

      // Send initial message if provided
      if (initialMessage) {
        await Message.sendMessage(
          conversation._id.toString(),
          userId,
          userType,
          initialMessage,
          'text'
        );
      }

      const response = {
        type: 'CONVERSATION_CREATED',
        requestId,
        success: true,
        data: {
          conversation,
          isNew: true
        },
        timestamp: new Date().toISOString()
      };

      clientInfo.ws.send(JSON.stringify(response));
    } catch (error) {
      console.error(`[${connectionId}] Error creating conversation for ${userId}:`, error);
      const errorResponse = {
        type: 'CONVERSATION_CREATED',
        requestId,
        success: false,
        error: error.message || 'Failed to create conversation',
        timestamp: new Date().toISOString()
      };
      clientInfo.ws.send(JSON.stringify(errorResponse));
    }
  }

  async handleGetMessages(clientInfo, message) {
    const { connectionId, userId, userType } = clientInfo;
    const { requestId, conversationId, page = 1, limit = 50 } = message;

    try {
      // Verify user has access to this conversation
      const conversation = await Conversation.findById(conversationId);
      if (!conversation) {
        throw new Error('Conversation not found');
      }

      const hasAccess = (userType === 'patient' && conversation.customerId === userId) ||
                       (userType === 'nurse' && conversation.nurseId === userId);

      if (!hasAccess) {
        throw new Error('Access denied');
      }

      const rawMessages = await Message.getConversationMessages(
        conversationId,
        parseInt(page),
        parseInt(limit)
      );

      // Transform messages to match frontend interface
      const messages = rawMessages.map(msg => ({
        id: msg._id.toString(),
        conversationId: msg.conversationId.toString(),
        senderId: msg.senderId,
        senderType: msg.senderType,
        senderName: msg.senderType === 'nurse' ? 'Nurse' : 'Patient', // TODO: Get actual names from user service
        content: msg.message, // Backend uses 'message', frontend expects 'content'
        type: msg.messageType || 'text',
        status: msg.status || 'sent',
        timestamp: msg.createdAt ? msg.createdAt.toISOString() : new Date().toISOString(),
        metadata: msg.metadata || {}
      }));

      const response = {
        type: 'MESSAGES_RESPONSE',
        requestId,
        success: true,
        data: {
          messages,
          pagination: {
            page: parseInt(page),
            limit: parseInt(limit),
            total: messages.length,
            totalPages: Math.ceil(messages.length / parseInt(limit))
          }
        },
        timestamp: new Date().toISOString()
      };

      clientInfo.ws.send(JSON.stringify(response));
    } catch (error) {
      console.error(`[${connectionId}] Error getting messages for ${userId}:`, error);
      const errorResponse = {
        type: 'MESSAGES_RESPONSE',
        requestId,
        success: false,
        error: error.message || 'Failed to fetch messages',
        timestamp: new Date().toISOString()
      };
      clientInfo.ws.send(JSON.stringify(errorResponse));
    }
  }

  async handleSearchMessages(clientInfo, message) {
    const { connectionId, userId, userType } = clientInfo;
    const { requestId, conversationId, query, page = 1, limit = 20 } = message;

    try {
      // Verify user has access to this conversation
      const conversation = await Conversation.findById(conversationId);
      if (!conversation) {
        throw new Error('Conversation not found');
      }

      const hasAccess = (userType === 'patient' && conversation.customerId === userId) ||
                       (userType === 'nurse' && conversation.nurseId === userId);

      if (!hasAccess) {
        throw new Error('Access denied');
      }

      const messages = await Message.searchMessages(
        conversationId,
        query,
        parseInt(page),
        parseInt(limit)
      );

      const response = {
        type: 'SEARCH_RESPONSE',
        requestId,
        success: true,
        data: {
          messages,
          pagination: {
            page: parseInt(page),
            limit: parseInt(limit),
            total: messages.length,
            totalPages: Math.ceil(messages.length / parseInt(limit))
          }
        },
        timestamp: new Date().toISOString()
      };

      clientInfo.ws.send(JSON.stringify(response));
    } catch (error) {
      console.error(`[${connectionId}] Error searching messages for ${userId}:`, error);
      const errorResponse = {
        type: 'SEARCH_RESPONSE',
        requestId,
        success: false,
        error: error.message || 'Failed to search messages',
        timestamp: new Date().toISOString()
      };
      clientInfo.ws.send(JSON.stringify(errorResponse));
    }
  }

  async handleMarkMessagesRead(clientInfo, message) {
    const { connectionId, userId, userType } = clientInfo;
    const { requestId, conversationId } = message;

    try {
      // Verify user has access to this conversation
      const conversation = await Conversation.findById(conversationId);
      if (!conversation) {
        throw new Error('Conversation not found');
      }

      const hasAccess = (userType === 'patient' && conversation.customerId === userId) ||
                       (userType === 'nurse' && conversation.nurseId === userId);

      if (!hasAccess) {
        throw new Error('Access denied');
      }

      // Get the messages that will be marked as read before updating them
      const messagesToUpdate = await Message.find({
        conversationId,
        senderId: { $ne: userId },
        status: { $ne: 'read' }
      }).select('_id senderId');

      const result = await Message.markMessagesAsRead(conversationId, userId);

      const response = {
        type: 'MESSAGES_MARKED_READ',
        requestId,
        success: true,
        conversationId,
        messagesMarked: result.modifiedCount,
        readBy: userId,
        readByName: clientInfo.username,
        readByType: userType,
        timestamp: new Date().toISOString()
      };

      clientInfo.ws.send(JSON.stringify(response));
      // Send MESSAGE_STATUS_UPDATE to each original sender
      if (messagesToUpdate.length > 0) {
        const senderIds = [...new Set(messagesToUpdate.map(msg => msg.senderId))];
        senderIds.forEach(senderId => {
          if (senderId !== userId) {
            // Get all message IDs for this sender
            const senderMessageIds = messagesToUpdate
              .filter(msg => msg.senderId === senderId)
              .map(msg => msg._id.toString());

            // Send status update for each message
            senderMessageIds.forEach(messageId => {
              this.sendToUser(senderId, {
                type: 'MESSAGE_STATUS_UPDATE',
                messageId: messageId,
                conversationId: conversationId,
                status: 'read',
                readBy: userId,
                readByName: clientInfo.username,
                readByType: userType,
                timestamp: new Date().toISOString()
              });
            });
          }
        });
      }

      // Broadcast read receipt to other participants
      this.broadcastToRoom(conversationId, {
        type: 'READ_RECEIPT',
        conversationId,
        senderId: userId,
        senderType: userType,
        senderName: clientInfo.username,
        timestamp: new Date().toISOString()
      }, userId);

      // Also broadcast a CONVERSATION_READ event for consistency with READ_RECEIPT handler
      this.broadcastToRoom(conversationId, {
        type: 'CONVERSATION_READ',
        conversationId,
        readBy: userId,
        readByName: clientInfo.username,
        readByType: userType,
        messagesMarked: result.modifiedCount,
        readAt: new Date().toISOString()
      }, userId);
    } catch (error) {
      console.error(`[${connectionId}] Error marking messages as read for ${userId}:`, error);
      const errorResponse = {
        type: 'MESSAGES_MARKED_READ',
        requestId,
        success: false,
        error: error.message || 'Failed to mark messages as read',
        timestamp: new Date().toISOString()
      };
      clientInfo.ws.send(JSON.stringify(errorResponse));
    }
  }

  async handleUpdateConversationStatus(clientInfo, message) {
    const { connectionId, userId, userType } = clientInfo;
    const { requestId, conversationId, status } = message;

    try {
      // Verify user has access to this conversation
      const conversation = await Conversation.findById(conversationId);
      if (!conversation) {
        throw new Error('Conversation not found');
      }

      const hasAccess = (userType === 'patient' && conversation.customerId === userId) ||
                       (userType === 'nurse' && conversation.nurseId === userId);

      if (!hasAccess) {
        throw new Error('Access denied');
      }

      // Update conversation status
      conversation.status = status;
      await conversation.save();

      const response = {
        type: 'CONVERSATION_STATUS_UPDATED',
        requestId,
        success: true,
        data: {
          conversation
        },
        timestamp: new Date().toISOString()
      };

      clientInfo.ws.send(JSON.stringify(response));
      // Broadcast status update to other participants
      this.broadcastToRoom(conversationId, {
        type: 'CONVERSATION_STATUS_UPDATED',
        conversationId,
        status,
        updatedBy: userId,
        timestamp: new Date().toISOString()
      }, userId);
    } catch (error) {
      console.error(`[${connectionId}] Error updating conversation status for ${userId}:`, error);
      const errorResponse = {
        type: 'CONVERSATION_STATUS_UPDATED',
        requestId,
        success: false,
        error: error.message || 'Failed to update conversation status',
        timestamp: new Date().toISOString()
      };
      clientInfo.ws.send(JSON.stringify(errorResponse));
    }
  }

  async handleGetUnreadCount(clientInfo, message) {
    const { connectionId, userId, userType } = clientInfo;
    const { requestId } = message;

    try {
      const conversations = await Conversation.getUserConversations(userId, userType, 1, 1000);

      let totalUnreadCount = 0;
      for (const conversation of conversations) {
        const unreadCount = await Message.getUnreadCountForUser(conversation._id.toString(), userId);
        totalUnreadCount += unreadCount;
      }

      const response = {
        type: 'UNREAD_COUNT_RESPONSE',
        requestId,
        success: true,
        data: {
          unreadCount: totalUnreadCount
        },
        timestamp: new Date().toISOString()
      };

      clientInfo.ws.send(JSON.stringify(response));
    } catch (error) {
      console.error(`[${connectionId}] Error getting unread count for ${userId}:`, error);
      const errorResponse = {
        type: 'UNREAD_COUNT_RESPONSE',
        requestId,
        success: false,
        error: error.message || 'Failed to get unread count',
        timestamp: new Date().toISOString()
      };
      clientInfo.ws.send(JSON.stringify(errorResponse));
    }
  }

  async handleGetNurseInfo(clientInfo, message) {
    const { connectionId, userId } = clientInfo;
    const { requestId, nurseId } = message;

    try {
      // For now, return basic nurse info
      // In a real implementation, you'd fetch from a nurse database
      const nurseInfo = {
        id: nurseId,
        name: `Nurse ${nurseId}`,
        profileImage: null
      };

      const response = {
        type: 'NURSE_INFO_RESPONSE',
        requestId,
        success: true,
        data: {
          nurse: nurseInfo
        },
        timestamp: new Date().toISOString()
      };

      clientInfo.ws.send(JSON.stringify(response));
    } catch (error) {
      console.error(`[${connectionId}] Error getting nurse info for ${userId}:`, error);
      const errorResponse = {
        type: 'NURSE_INFO_RESPONSE',
        requestId,
        success: false,
        error: error.message || 'Failed to get nurse info',
        timestamp: new Date().toISOString()
      };
      clientInfo.ws.send(JSON.stringify(errorResponse));
    }
  }
}

module.exports = WebSocketManager;
